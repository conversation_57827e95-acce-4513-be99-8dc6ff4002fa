// ======================================
// MAIN NAVIGATION AND MENU FUNCTIONALITY
// ======================================

// Main menu toggle functionality
let menu = document.querySelector('#menu-icon');
let navbar = document.querySelector('.navbar');

if (menu && navbar) {
    menu.onclick = () => {
        menu.classList.toggle('bx-x');
        navbar.classList.toggle('active');
    }

    window.onscroll = () => {
        menu.classList.remove('bx-x');
        navbar.classList.remove('active');
    }
}

// ======================================
// TYPED.JS ANIMATIONS FOR HOME PAGE
// ======================================

// Check if Typed is available before using it
if (typeof Typed !== 'undefined') {
    // Home page typing animation
    const homeElement = document.querySelector('.home-mt');
    if (homeElement) {
        const home = new Typed('.home-mt', {
            strings: ['OS Linus Tools','Websites','Web/Apps Designs','Python Projects'],
            typeSpeed: 80,
            backSpeed: 80,
            backDelay: 1200,
            loop: true,
        });
    }

    // Course page typing animations
    const cyElement = document.querySelector('.cours-y');
    if (cyElement) {
        const cy = new Typed('.cours-y', {
            strings: ['MY YouTube Courses '],
            typeSpeed: 80,
            backSpeed: 80,
            backDelay: 1200,
            loop: true,
        });
    }

    const cwElement = document.querySelector('.cours-w');
    if (cwElement) {
        const cw = new Typed('.cours-w', {
            strings: ['My Website Courses '],
            typeSpeed: 80,
            backSpeed: 80,
            backDelay: 1200,
            loop: true,
        });
    }

    const coElement = document.querySelector('.cours-o');
    if (coElement) {
        const co = new Typed('.cours-o', {
            strings: ['Other Courses '],
            typeSpeed: 80,
            backSpeed: 80,
            backDelay: 1200,
            loop: true,
        });
    }
}

// ======================================
// COURSES PAGE FUNCTIONALITY
// ======================================

// DOM Elements for courses page
const menuIcon = document.querySelector('.menu-icon');
const sidebar = document.querySelector('.sidebar');
const mainContent = document.querySelector('.main-content');
const tabs = document.querySelectorAll('.tab');
const sidebarTabs = document.querySelectorAll('.sidebar-tab');
const videoCards = document.querySelectorAll('.video-card');

// Toggle sidebar visibility on mobile (courses page)
if (menuIcon && sidebar) {
    menuIcon.addEventListener('click', () => {
        // Check if we're on mobile (using window width as indicator)
        const isMobile = window.innerWidth <= 778;
        
        if (isMobile) {
            // Toggle the 'active' class on the sidebar for mobile view
            sidebar.classList.toggle('active');
        }
        // Don't do anything special on desktop - keep the sidebar visible as normal
    });
}

// Sidebar Category Tab Switching
if (sidebarTabs.length > 0) {
    sidebarTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all sidebar tabs
            sidebarTabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked sidebar tab
            tab.classList.add('active');
            
            // Get the selected category
            const category = tab.textContent.trim();
            
            // Update the content header to reflect the selected category
            const contentHeader = document.querySelector('.content-header h2');
            if (contentHeader) {
                if (category === 'All') {
                    contentHeader.textContent = 'Channels Name';
                } else {
                    contentHeader.textContent = category;
                }
            }
            
            // Also update the main category tabs to match the sidebar selection
            tabs.forEach(mainTab => {
                if (mainTab.textContent.trim() === category) {
                    // Remove active class from all main tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    // Add active class to matching main tab
                    mainTab.classList.add('active');
                }
            });
            
            console.log(`Sidebar category selected: ${category}`);
            
            // Here you would typically filter video content based on the selected category
            // For example: filterVideosByCategory(category);
        });
    });
}

// Sidebar item selection
const sidebarItems = document.querySelectorAll('.sidebar-item');
if (sidebarItems.length > 0) {
    sidebarItems.forEach(item => {
        item.addEventListener('click', () => {
            // Remove active class from all items
            sidebarItems.forEach(i => i.classList.remove('active'));
            
            // Add active class to clicked item
            item.classList.add('active');
            
            // Here you would typically load different content based on the selected item
            console.log(`Sidebar item clicked: ${item.textContent.trim()}`);
        });
    });
}

// Close sidebar when clicking outside of it on mobile
if (sidebar && menuIcon) {
    document.addEventListener('click', (e) => {
        const isMobile = window.innerWidth <= 768;
        if (isMobile && sidebar.classList.contains('active')) {
            // Check if the click is outside the sidebar and not on the menu icon
            if (!sidebar.contains(e.target) && !menuIcon.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        }
    });
}

// Make sure sidebar is properly initialized on page load
window.addEventListener('DOMContentLoaded', () => {
    if (sidebar) {
        // Check if we're on mobile on initial load
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // Make sure sidebar is in the correct initial state on mobile
            sidebar.classList.remove('active');
            sidebar.style.left = ''; // Ensure CSS handles the positioning
        }
    }
});

// Handle window resize to reset sidebar state
window.addEventListener('resize', () => {
    if (sidebar && mainContent) {
        const isMobile = window.innerWidth <= 758;
        
        if (isMobile) {
            // On mobile: reset any desktop-specific styles
            sidebar.style.width = '';
            mainContent.style.marginLeft = '';
            document.querySelectorAll('.sidebar-item span').forEach(span => {
                span.style.display = '';
            });
            
            // Ensure sidebar is properly hidden on initial mobile view
            if (!sidebar.classList.contains('active')) {
                sidebar.style.left = '-220px';
            }
        } else {
            // On desktop: reset mobile-specific styles
            sidebar.classList.remove('active');
            sidebar.style.left = ''; // Reset inline style
        }
    }
});

// Function to filter videos by category (placeholder implementation)
function filterVideosByCategory(category) {
    // Get all video cards
    const videos = document.querySelectorAll('.video-card');
    
    // If 'All' is selected, show all videos
    if (category === 'All') {
        videos.forEach(video => {
            video.style.display = 'block';
        });
        return;
    }
    
    // Otherwise, filter based on category
    videos.forEach(video => {
        const channelName = video.querySelector('.channel-name');
        if (channelName && channelName.textContent === category) {
            video.style.display = 'block';
        } else {
            video.style.display = 'none';
        }
    });
}

// Video card hover effects
if (videoCards.length > 0) {
    videoCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.3)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = 'none';
        });
        
        // Add click event to video cards
        card.addEventListener('click', () => {
            const videoTitle = card.querySelector('.video-title');
            if (videoTitle) {
                console.log(`Video clicked: ${videoTitle.textContent}`);
                // Here you would typically navigate to the video page
            }
        });
    });
}

// ======================================
// VIDEO PLAYER FUNCTIONALITY
// ======================================

// Function to open video (used by course pages)
function openVideo(src, title, channel) {
    // Encode the parameters to ensure they work in a URL
    const encodedSrc = encodeURIComponent(src);
    const encodedTitle = encodeURIComponent(title);
    const encodedChannel = encodeURIComponent(channel);

    // Navigate to the watch page with the parameters
    window.location.href = `watch.html?src=${encodedSrc}&title=${encodedTitle}&channel=${encodedChannel}`;
}

// Make openVideo function globally accessible
window.openVideo = openVideo;

// ======================================
// GOOGLE ANALYTICS (if needed)
// ======================================

// Google Analytics initialization
if (typeof gtag === 'function') {
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-5M2XQB5KVD');
}

