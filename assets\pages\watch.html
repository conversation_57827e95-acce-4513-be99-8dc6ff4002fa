<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watch Video</title>
    <link rel="icon" href="../images/me.jpg">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

</head>

<body>
      <!-- HEADER SECTION -->
        <header class="header">
            <a href="../../index.html" class="logo"><PERSON><PERSON> khan</a>
    
                    <img src="../SVG/hamburger-menu-icon.svg" id="menu-icon"></img>

    
            <nav class="navbar">
                <a href="../../index.html">Home</a>
            <a href="assets/pages/documentation.html">Documentation</a>
                <a href="about.html">About</a>
                <a href="projects.html">Projects</a>
                <a href="courses.html">Courses</a>
                <a href="contact.html">Contact</a>
            </nav>
        </header>
    <!-- copy blog -->

    <!-- Navigation Bar -->
    <div class="c-navbar">
        <div class="navbar-left">

            <div class="logo-small">
                <h2 onclick="window.location.href='../../index.html'">PlayList</h2>
            </div>
        </div>
        <div class="navbar-right">
            <button class="home-btn" onclick="history.back()">
                <!-- <i class="fas fa-home"></i> -->
                X</button>
        </div>
    </div>




    <div class="row">



        <!-- left column -->
        <div class="leftcolumn">
            <!-- card 1 -->
            <div class="video-container pornhub-style card">
                <h2></h2>
                <h5></h5>
                <!-- Left side - Video player and info -->
                <div class="video-main-column">
                    <div class="video-player-wrapper">
                        <div class="video-player" id="video-player">
                            <!-- Video will be loaded here by JavaScript -->
                        </div>
                    </div>


                    <div class="video-info">
                        <h1 class="video-title" id="video-title"></h1>
                        <span class="channel-name" id="channel-name"></span>
                    </div>

                </div>
                <p></p>
            </div>
        </div>

        <!-- right column -->
        <div class="rightcolumn">

            <div class="card">
                <h3>ADS</h3>

                
                <!-- <div class="fakeimg">Image</div><br> -->
                <!-- <div class="fakeimg">Image</div><br> -->
                <!-- <div class="fakeimg">Image</div> -->
            </div>
           
            <div class="card">
             
            </div>
            <div class="card">
                <!-- <h3>Follow Me</h3> -->
                <!-- <p>Some text..</p> -->
            </div>
        </div>

    </div>
    <div class="related-videos-section">
        <h2>Related Videos</h2>
        
            <div class="video-grid">

                 <!-- Video Card 3 -->
                <div class="video-card"
                    onclick="openVideo('https://drive.google.com/file/d/1AV9_Al30ChqkKDihjuvtv8BNNgA0Nqt/preview', 'Third ', 'channel', '3.7M views', '3 days ago')">
                    <div class="thumbnail">
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">channel</p>
                    </div>
                </div>

                <!-- Video Card 4 -->
                <div class="video-card"
                    onclick="openVideo('https://drive.google.com/file/d/1ZMa3VKp9n4VdfwsHhTQUpdCof5Blw/preview', 'Fourth Amazing Video Title', 'channel', '1.8M views', '5 days ago')">
                    <div class="thumbnail">
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Fourth Amazing Video Title</h3>
                        <p class="channel-name">channel</p>
                    </div>
                </div>

                <!-- Video Card 5 -->
                <div class="video-card"
                    onclick="openVideo('https://drive.google.com/embed/67fb92310fd', '', 'channel', '950K views', '1 day ago')">
                    <div class="thumbnail">
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Amazing Video Title</h3>
                        <p class="channel-name">channel</p>
                    </div>
                </div>

                <!-- Video Card  -->
                <div class="video-card"
                    onclick="openVideo('https://drive.google.com/embed/680366a74f', '', 'channel', '2.1M views', '4 days ago')">
                    <div class="thumbnail">
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Amazing Video Title</h3>
                        <p class="channel-name">channel</p>
                    </div>
                </div>
               
                <!-- Video Card 4 -->
                <div class="video-card"
                    onclick="openVideo('https://drive.google.com/embed/68146bf4dacaa', 'title', 'Abella Danger', '1.8M views', '5 days ago')">
                    <div class="thumbnail">
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">title</h3>
                        <p class="channel-name">channel</p>
                    </div>
                </div>

                <!-- Video Card 1 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/W1iXIiF5iMw?si=VnSaY3QueZYTdlu8" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 2 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/_5CY-p0Qt4U?si=5kESF8VwzLLi37Hn" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 3 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/UWKx5LKyduE?si=hST5OpqpxosHaYs5" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 4 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/_MFw95d49ok?si=ljas4z_3lJ31Gy_L" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 5 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/O69N6Gk3eaw?si=XFu7rpNcHEF1Wd-Q" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 6 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/BPdyZpXW9F8?si=6G2HBF9gUz9S9P_H" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 7 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/WRI4fXDIXWM?si=xmAzaKWXyDbzkYMb" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 8 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/ZsRMQHbx6Xc?si=azLR6cpwzcGVBm7b" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 9 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/C2QfkDcQ5MU?si=uZoHYrp8sRlnSzp3" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 10 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/C2QfkDcQ5MU?si=SJqQnAJlddjq-3bU" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 11 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/GGdmIL85qos?si=luOnYPpnGYpoddmm" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 12 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/68YPgQE_zm4?si=oKxx_l6ZpfbysYnk" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 13 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/GdBV4irqOd8?si=z_A_skXLSVkc2jSg" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 14 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/gGukPkjBDdo?si=Xw3QCh4zXByMnpLl" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 15 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/RF26NjCUNvs?si=qg114TNUK2VQBYvi" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

                <!-- Video Card 16 - YouTube Video -->
                <div class="video-card">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/M-0LZH7KHkQ?si=ZARlHx3_xuJOA4Ij" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>

            </div>
    </div>

    <!-- copy blog end -->
    <script src="../js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Get URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const videoSrc = urlParams.get('src');
            const videoTitle = urlParams.get('title');
            const channelName = urlParams.get('channel');
            const viewCount = urlParams.get('views');
            const uploadDate = urlParams.get('date');

            // Set video player content
            if (videoSrc) {
                let finalSrc = videoSrc;
                // Check if it's a Google Drive preview link
                if (videoSrc.includes('drive.google.com/file/') && videoSrc.includes('/preview')) {
                    // Add autoplay=1 parameter
                    if (videoSrc.includes('?')) {
                        finalSrc += '&autoplay=1';
                    } else {
                        finalSrc += '?autoplay=1';
                    }
                }
                // If it's an iframe source
                document.getElementById('video-player').innerHTML = `<iframe src="${finalSrc}" frameborder="0" scrolling="no" allowfullscreen width="100%" height="100%" allow="autoplay; fullscreen"></iframe>`;
            } else {
                // If it's an image, we'll show the image and simulate a video player
                document.getElementById('video-player').innerHTML = `
                    <div class="video-image-container">
                        <img src="${videoSrc}" alt="Video">
                        <div class="play-icon-overlay">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                `;
            }

            // Set video details
            if (videoTitle) document.getElementById('video-title').textContent = videoTitle;
            if (channelName) document.getElementById('channel-name').textContent = channelName;

            // Set page title
            if (videoTitle) document.title = `${videoTitle} - Watch`;
        });
    </script>
</body>

</html>