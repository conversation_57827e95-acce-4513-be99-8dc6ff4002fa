<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A<PERSON><PERSON><PERSON><PERSON> khan - Courses</title>
    <link rel="icon" href="../images/me.jpg">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

</head>

<body>

    <!-- HEADER SECTION -->
    <header class="header">
        <a href="../../index.html" class="logo"><PERSON><PERSON><PERSON><PERSON><PERSON> khan</a>

        <img src="../SVG/hamburger-menu-icon.svg" id="menu-icon"></img>


        <nav class="navbar">
            <a href="../../index.html">Home</a>
            <a href="documentation.html">Documentation</a>
            <a href="about.html">About</a>
            <a href="projects.html">Projects</a>
            <a href="courses.html" class="active">Courses</a>
            <a href="contact.html">Contact</a>
        </nav>
    </header>
    <br>
    <!-- Navigation Bar -->
    <div class="c-navbar">
        <div class="navbar-left">
            <div class="menu-icon">
                <i class="fas fa-bars">a</i>
            </div>
            <div class="logo-small">
                <h2>Couses</h2>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="content-container">
        <!-- Sidebar -->
        <div class="sidebar">


            <!-- Category Tabs in Sidebar -->
            <div class="sidebar-category">

                <div class="sidebar-tabs">
                    <div class="sidebar-tab" onclick="window.location.href='courses.html'">Home</div>

                    <div class="sidebar-tab active" onclick="window.location.href='courses-python.html'">Python</div>
                    <div class="sidebar-tab" onclick="window.location.href='courses-web.html'">Website Development</div>
                    <div class="sidebar-tab" onclick="window.location.href='courses-mysql.html'">MySQL</div>

                </div>
            </div>


        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
            </div>

            <div class="video-grid">

                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/W1iXIiF5iMw?si=VnSaY3QueZYTdlu8', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/W1iXIiF5iMw?si=VnSaY3QueZYTdlu8"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/_5CY-p0Qt4U?si=5kESF8VwzLLi37Hn', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/_5CY-p0Qt4U?si=5kESF8VwzLLi37Hn"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/UWKx5LKyduE?si=hST5OpqpxosHaYs5', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/UWKx5LKyduE?si=hST5OpqpxosHaYs5"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/_MFw95d49ok?si=ljas4z_3lJ31Gy_L', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/_MFw95d49ok?si=ljas4z_3lJ31Gy_L"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/O69N6Gk3eaw?si=XFu7rpNcHEF1Wd-Q', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/O69N6Gk3eaw?si=XFu7rpNcHEF1Wd-Q"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/BPdyZpXW9F8?si=6G2HBF9gUz9S9P_H', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/BPdyZpXW9F8?si=6G2HBF9gUz9S9P_H"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/WRI4fXDIXWM?si=xmAzaKWXyDbzkYMb', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/WRI4fXDIXWM?si=xmAzaKWXyDbzkYMb"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/ZsRMQHbx6Xc?si=azLR6cpwzcGVBm7b', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/ZsRMQHbx6Xc?si=azLR6cpwzcGVBm7b"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/C2QfkDcQ5MU?si=uZoHYrp8sRlnSzp3', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/C2QfkDcQ5MU?si=uZoHYrp8sRlnSzp3"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/C2QfkDcQ5MU?si=SJqQnAJlddjq-3bU', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/C2QfkDcQ5MU?si=SJqQnAJlddjq-3bU"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/GGdmIL85qos?si=luOnYPpnGYpoddmm', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/GGdmIL85qos?si=luOnYPpnGYpoddmm"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/68YPgQE_zm4?si=oKxx_l6ZpfbysYnk', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/68YPgQE_zm4?si=oKxx_l6ZpfbysYnk"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/GdBV4irqOd8?si=z_A_skXLSVkc2jSg', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/GdBV4irqOd8?si=z_A_skXLSVkc2jSg"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/gGukPkjBDdo?si=Xw3QCh4zXByMnpLl', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/gGukPkjBDdo?si=Xw3QCh4zXByMnpLl"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/RF26NjCUNvs?si=qg114TNUK2VQBYvi', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/RF26NjCUNvs?si=qg114TNUK2VQBYvi"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>
                <!-- Video Card  -->
                <div class="video-card" onclick="openVideo('https://www.youtube.com/embed/M-0LZH7KHkQ?si=ZARlHx3_xuJOA4Ij', 'title', 'Code with Kylie' )">
                    <div class="thumbnail">
                        <iframe width="560" height="315"
                            src="https://www.youtube.com/embed/M-0LZH7KHkQ?si=ZARlHx3_xuJOA4Ij"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Third Amazing Video Title</h3>
                        <p class="channel-name">Code with Kylie</p>
                    </div>
                </div>


            </div>

        </div>

    </div>
    </div>


    <script src="../js/script.js"></script>

    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-5M2XQB5KVD');
    </script>
    <script>
        function openVideo(src, title, channel, views, date) {
            // Encode the parameters to ensure they work in a URL
            const encodedSrc = encodeURIComponent(src);
            const encodedTitle = encodeURIComponent(title);
            const encodedChannel = encodeURIComponent(channel);
            const encodedViews = encodeURIComponent(views);
            const encodedDate = encodeURIComponent(date);

            // Navigate to the watch page with the parameters
            window.location.href = `watch.html?src=${encodedSrc}&title=${encodedTitle}&channel=${encodedChannel}&views=${encodedViews}&date=${encodedDate}`;
        }
    </script>
</body>

</html>