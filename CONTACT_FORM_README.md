# Contact Form Setup and Usage Guide

## Overview
This contact form system allows visitors to send messages through your website, which are then stored in a MySQL database. The system includes form validation, AJAX submission, and an admin panel to view messages.

## Files Created/Modified

### New Files:
- `assets/pages/save_contact.php` - Handles form submission and database operations
- `assets/pages/config.php` - Database configuration and connection functions
- `assets/pages/setup_database.php` - Database setup script
- `assets/pages/view_contacts.php` - Admin panel to view submitted messages

### Modified Files:
- `assets/pages/contact.html` - Updated with improved form handling and AJAX submission

## Setup Instructions

### 1. Prerequisites
- XAMPP (or similar) with Apache and MySQL running
- PHP 7.0 or higher
- MySQL 5.6 or higher

### 2. Database Setup
1. Start XAMPP (Apache and MySQL services)
2. Open your browser and navigate to: `http://localhost/your-project-folder/assets/pages/setup_database.php`
3. Click "Setup Database" to create the database and table automatically
4. You should see a success message confirming the setup

### 3. Configuration (Optional)
If you need to change database settings, edit `assets/pages/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'contact_db');
```

## Features

### Contact Form (`contact.html`)
- **Client-side validation**: Checks for required fields and email format
- **AJAX submission**: Form submits without page reload
- **Real-time feedback**: Shows success/error messages
- **Responsive design**: Works on all devices

### Backend Processing (`save_contact.php`)
- **Server-side validation**: Validates and sanitizes all input
- **Database storage**: Saves messages to MySQL database
- **JSON responses**: Returns structured responses for AJAX
- **Error handling**: Comprehensive error handling and logging

### Admin Panel (`view_contacts.php`)
- **Message viewing**: Display all submitted messages
- **Sorting**: Messages sorted by newest first
- **Statistics**: Shows total message count
- **Responsive table**: Easy to read on all devices

## Database Structure

The system creates a `contact_db` database with a `contacts` table:

```sql
CREATE TABLE contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Usage

### For Visitors:
1. Navigate to the contact page
2. Fill in the required fields (Name, Email, Message)
3. Optionally add phone number
4. Click "Send Message"
5. Receive confirmation message

### For Admin:
1. Access `http://localhost/your-project-folder/assets/pages/view_contacts.php`
2. View all submitted messages
3. Messages are displayed with timestamps
4. Use refresh button to see new messages

## Security Features

- **Input sanitization**: All user input is cleaned and validated
- **SQL injection prevention**: Uses prepared statements
- **XSS protection**: HTML special characters are escaped
- **Email validation**: Server-side email format validation

## Troubleshooting

### Common Issues:

1. **Database connection failed**
   - Check if MySQL is running in XAMPP
   - Verify database credentials in `config.php`
   - Run the setup script again

2. **Form not submitting**
   - Check browser console for JavaScript errors
   - Ensure `save_contact.php` is accessible
   - Verify file permissions

3. **Messages not appearing**
   - Check if database and table exist
   - Verify data is being inserted (check phpMyAdmin)
   - Ensure proper file paths

### Testing the Setup:
1. Submit a test message through the contact form
2. Check for success message
3. View the message in the admin panel
4. Verify data in phpMyAdmin (optional)

## File Permissions
Ensure PHP files have proper read permissions:
- `save_contact.php` - Read/Execute
- `config.php` - Read
- `setup_database.php` - Read/Execute
- `view_contacts.php` - Read/Execute

## Next Steps
- Add email notifications when new messages are received
- Implement message status tracking (read/unread)
- Add message deletion functionality
- Implement user authentication for admin panel
- Add export functionality for messages

## Support
If you encounter any issues, check:
1. XAMPP error logs
2. Browser developer console
3. PHP error logs
4. Database connection status

The system is now ready to handle contact form submissions and store them in your database!
